# Alden

A modern SaaS application built with Next.js, TypeScript, Tailwind CSS, shadcn/ui, and Clerk for authentication and multitenancy.

## Features

- 🔐 **Authentication**: Complete auth flow with <PERSON> (sign-in, sign-up, user management)
- 🏢 **Multitenancy**: Organization-based multitenancy with automatic organization selection
- 🎨 **Modern UI**: Built with shadcn/ui components and Tailwind CSS
- 📱 **Responsive**: Mobile-first design with responsive sidebar
- ⚡ **Fast**: Built with Next.js App Router and TypeScript

## Getting Started

### Prerequisites

- Node.js 18+
- pnpm (recommended)
- A Clerk account for authentication

### Setup

1. **Clone and install dependencies:**
   ```bash
   pnpm install
   ```

2. **Configure Clerk:**
   - Create a new application in [Clerk Dashboard](https://dashboard.clerk.com)
   - Enable Organizations in your Clerk application settings
   - Copy your keys to `.env.local`:
   ```bash
   NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=your_publishable_key_here
   CLERK_SECRET_KEY=your_secret_key_here
   NEXT_PUBLIC_CLERK_SIGN_IN_URL=/sign-in
   NEXT_PUBLIC_CLERK_SIGN_UP_URL=/sign-up
   NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL=/
   NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL=/
   ```

3. **Run the development server:**
   ```bash
   pnpm dev
   ```

4. **Open [http://localhost:3000](http://localhost:3000)** in your browser.

## Project Structure

```
src/
├── app/                    # Next.js App Router pages
│   ├── home/              # Dashboard home page
│   ├── organization/      # Organization management page
│   ├── sign-in/          # Clerk sign-in page
│   ├── sign-up/          # Clerk sign-up page
│   └── layout.tsx        # Root layout with ClerkProvider
├── components/           # Reusable components
│   ├── ui/              # shadcn/ui components
│   ├── dashboard-layout.tsx
│   ├── header.tsx
│   └── sidebar.tsx
├── lib/                 # Utilities
└── middleware.ts        # Clerk middleware for route protection
```

## Authentication Flow

1. **Unauthenticated users** are redirected to `/sign-in`
2. **Authenticated users** are redirected based on their organizations:
   - **One organization**: Auto-selected and redirected to `/home`
   - **Zero or multiple organizations**: Redirected to `/home` with organization switcher

## Available Pages

- **`/`** - Root page with multitenancy logic
- **`/home`** - Dashboard home (empty content area)
- **`/organization`** - Organization management with Clerk's OrganizationProfile
- **`/sign-in`** - Authentication sign-in page
- **`/sign-up`** - Authentication sign-up page

## Tech Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS v4
- **UI Components**: shadcn/ui
- **Authentication**: Clerk
- **Icons**: Lucide React
- **Package Manager**: pnpm

## Deployment

This app is optimized for deployment on Vercel:

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Add your Clerk environment variables in Vercel dashboard
4. Deploy!

## License

MIT
