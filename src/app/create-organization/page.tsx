'use client';

import { CreateOrganization, useAuth, useOrganizationList } from '@clerk/nextjs';
import { useRouter } from 'next/navigation';

export default function CreateOrganizationPage() {
    // check if authorized, or if he has any orgs, if not authorized, redirect to /sign-in
    // if he has any orgs, redirect to /home

    const { isLoaded, isSignedIn,  } = useAuth();
    const router = useRouter();
    const { userMemberships, isLoaded: isOrgLoaded } = useOrganizationList({userMemberships: true});

    if (!isLoaded || !isOrgLoaded) return null;

    if (!isSignedIn) {
        router.push('/sign-in');
        return;
    }

    if (userMemberships.count) {
        // if not selected org, select first
        router.push('/home');
        return;
    }

  console.log({userMemberships, isOrgLoaded})

  return (
    <div className="flex min-h-screen items-center justify-center bg-background">
        <CreateOrganization skipInvitationScreen />
    </div>
  );
}
