'use client';

import { useEffect } from 'react';
import { useAuth, useOrganizationList } from '@clerk/nextjs';
import { useRouter } from 'next/navigation';

export default function RootPage() {
  const { isLoaded, isSignedIn } = useAuth();
  const { userMemberships } = useOrganizationList({userMemberships: true});
  const router = useRouter();

  // more of ssr, not this

  // useEffect(() => {
  //   if (!isLoaded) return;

  //   if (!isSignedIn) {
  //     router.push('/sign-in');
  //     return;
  //   }

  //   if (userMemberships && userMemberships.count === 0) {
  //     router.push('/create-organization');
  //     return;
  //   }

  // }, [isLoaded, isSignedIn, router]);

  // Show loading state while checking auth and organizations
  if (!isLoaded) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-muted-foreground">Loading...</div>
      </div>
    );
  }

  return "this is an actual page...";
}
