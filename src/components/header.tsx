'use client';

import { OrganizationSwitcher, UserButton } from '@clerk/nextjs';
import { Input } from '@/components/ui/input';
import { Search } from 'lucide-react';

export function Header() {
  return (
    <header className="border-b border-border bg-background">
      <div className="flex items-center justify-between px-6 py-4">
        {/* Search */}
        <div className="flex-1 max-w-md">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
            <Input
              placeholder="Ask AI"
              className="pl-10 bg-muted/50 border-0 focus-visible:ring-1"
            />
          </div>
        </div>

        {/* User controls */}
        <div className="flex items-center space-x-4">
          <OrganizationSwitcher 
            afterSelectOrganizationUrl="/home"
            hidePersonal
            hideSlug
            // appearance={{
            //   elements: {
            //     organizationSwitcherTrigger: "border border-border rounded-lg px-3 py-2 hover:bg-muted",
            //   }
            // }}
          />
          <UserButton 
            appearance={{
              elements: {
                avatarBox: "w-8 h-8",
              }
            }}
          />
        </div>
      </div>
    </header>
  );
}
